/**
 * 🎮 策略控制台场景
 * 集成策略管理、监控和实验室功能的统一界面
 */

import React, { useState, useCallback } from 'react'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'

// 导入页面组件
import MyStrategiesPage from '../components/StrategyConsole/MyStrategiesPage'
import MonitoringRoomPage from '../components/StrategyConsole/MonitoringRoomPage'
import LabPage from '../components/StrategyConsole/LabPage'

// 🎯 页面类型定义
type ConsolePage = 'strategies' | 'monitoring' | 'lab'

// 📋 页面配置
const PAGES = [
  {
    key: 'strategies' as ConsolePage,
    label: '我的策略',
    icon: '📊',
    component: MyStrategiesPage
  },
  {
    key: 'monitoring' as ConsolePage,
    label: '监控室',
    icon: '📡',
    component: MonitoringRoomPage
  },
  {
    key: 'lab' as ConsolePage,
    label: '实验室',
    icon: '🧪',
    component: LabPage
  }
]

// 🎯 策略控制台主组件
function StrategyConsoleScene() {
  const [currentPage, setCurrentPage] = useState<ConsolePage>('strategies')
  const [isTransitioning, setIsTransitioning] = useState(false)

  // 🔄 页面切换处理
  const handlePageChange = useCallback((page: ConsolePage) => {
    if (page === currentPage || isTransitioning) return
    
    setIsTransitioning(true)
    setCurrentPage(page)
    
    // 重置过渡状态
    setTimeout(() => {
      setIsTransitioning(false)
    }, 300)
  }, [currentPage, isTransitioning])

  // 🎯 获取当前页面组件
  const getCurrentPageComponent = () => {
    const page = PAGES.find(p => p.key === currentPage)
    return page?.component || MyStrategiesPage
  }

  const CurrentPageComponent = getCurrentPageComponent()

  return (
    <UnifiedMobileNav title="策略控制台">
      {/* 标签页导航 */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        margin: '1rem',
        background: '#f5f5f5',
        borderRadius: '12px',
        padding: '4px',
        boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {PAGES.map((page) => (
          <button
            key={page.key}
            style={{
              flex: 1,
              padding: '0.75rem 1rem',
              border: 'none',
              borderRadius: '8px',
              fontSize: '0.9rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative',
              zIndex: 2,
              background: currentPage === page.key ? '#667eea' : 'transparent',
              color: currentPage === page.key ? 'white' : '#666'
            }}
            onClick={() => handlePageChange(page.key)}
            disabled={isTransitioning}
            onMouseEnter={(e) => {
              if (!isTransitioning) {
                e.currentTarget.style.background = currentPage === page.key ? '#5a67d8' : 'rgba(102, 126, 234, 0.1)'
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = currentPage === page.key ? '#667eea' : 'transparent'
            }}
          >
            <span style={{ marginRight: '0.5rem' }}>{page.icon}</span>
            {page.label}
          </button>
        ))}
      </div>

      {/* 内容区域 */}
      <div style={{
        flex: 1,
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          overflowY: 'auto',
          overflowX: 'hidden'
        }}>
          <CurrentPageComponent />
        </div>
      </div>
    </UnifiedMobileNav>
  )
}

export default StrategyConsoleScene
