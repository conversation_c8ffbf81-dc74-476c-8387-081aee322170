/**
 * 🎮 策略控制台场景 - 重写版
 * 集成策略管理、监控和实验室功能的统一界面
 */

import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'
import { useStrategyState, useUIState } from '../../store/hooks'

// 🎯 页面类型定义
type ConsolePage = 'strategies' | 'monitoring' | 'lab'

// 🎨 样式组件
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
`

const TabBar = styled.div`
  display: flex;
  justify-content: center;
  margin: 1rem;
  background: #f5f5f5;
  border-radius: 12px;
  padding: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
`

const TabButton = styled(motion.button)<{ $active: boolean }>`
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: ${props => props.$active ? '#667eea' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#666'};
  
  &:hover {
    background: ${props => props.$active ? '#5a67d8' : 'rgba(102, 126, 234, 0.1)'};
  }
`

const ContentArea = styled.div`
  flex: 1;
  padding: 0 1rem 1rem;
  overflow-y: auto;
`

const PageSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const StrategyCard = styled(motion.div)`
  background: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
  }
`

const StrategyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
`

const StrategyName = styled.h4`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
`

const StrategyMeta = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
`

const Badge = styled.span<{ $variant: 'status' | 'type' | 'count' }>`
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  
  ${props => {
    switch (props.$variant) {
      case 'status':
        return `
          background: #10b98120;
          color: #10b981;
        `
      case 'type':
        return `
          background: #3b82f620;
          color: #3b82f6;
        `
      case 'count':
        return `
          background: #6b728020;
          color: #6b7280;
        `
      default:
        return ''
    }
  }}
`

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1rem;
`

const ActionButton = styled(motion.button)<{ $variant: 'primary' | 'secondary' }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  
  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  ` : `
    background: #f8fafc;
    color: #333;
    border: 1px solid #e2e8f0;
  `}
  
  &:hover {
    transform: translateY(-1px);
  }
`

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
`

const LoadingState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
`

// 🎮 策略控制台主组件
function StrategyConsoleScene() {
  const [currentPage, setCurrentPage] = useState<ConsolePage>('strategies')
  const { groups, loading, error, loadGroups } = useStrategyState()
  const { switchScene } = useUIState()

  // 🔄 加载策略组
  useEffect(() => {
    console.log('StrategyConsoleScene mounted, loading groups...')
    loadGroups()
  }, [loadGroups])

  // 调试日志
  useEffect(() => {
    console.log('Strategy data:', { groups, loading, error })
  }, [groups, loading, error])

  // 📋 页面配置
  const pages = [
    { key: 'strategies' as ConsolePage, label: '我的策略', icon: '📊' },
    { key: 'monitoring' as ConsolePage, label: '监控室', icon: '📡' },
    { key: 'lab' as ConsolePage, label: '实验室', icon: '🧪' }
  ]

  // 🎯 渲染策略列表页面
  const renderStrategiesPage = () => {
    console.log('Rendering strategies page, groups:', groups)
    
    return (
      <PageSection>
        <SectionTitle>
          📊 我的策略
        </SectionTitle>
        
        {loading ? (
          <LoadingState>加载中...</LoadingState>
        ) : error ? (
          <EmptyState>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⚠️</div>
            <div>加载失败: {error}</div>
          </EmptyState>
        ) : groups.length === 0 ? (
          <EmptyState>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
            <div>暂无策略组合</div>
          </EmptyState>
        ) : (
          groups.map((strategy, index) => (
            <StrategyCard
              key={strategy.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <StrategyHeader>
                <div>
                  <StrategyName>{strategy.name}</StrategyName>
                  <StrategyMeta>
                    <Badge $variant="status">
                      {strategy.status === 'active' ? '运行中' : '已停止'}
                    </Badge>
                    <Badge $variant="type">
                      {strategy.group_type === 'timing' ? '择时策略' : '选股策略'}
                    </Badge>
                    <Badge $variant="count">
                      {strategy.cards?.length || 0} 张卡片
                    </Badge>
                  </StrategyMeta>
                </div>
              </StrategyHeader>
              
              {strategy.description && (
                <div style={{ 
                  fontSize: '0.85rem', 
                  color: '#666', 
                  marginBottom: '1rem',
                  lineHeight: 1.4 
                }}>
                  {strategy.description}
                </div>
              )}
              
              <ActionButtons>
                <ActionButton
                  $variant="secondary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => console.log('编辑策略:', strategy.id)}
                >
                  编辑
                </ActionButton>
                <ActionButton
                  $variant="primary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => console.log('执行策略:', strategy.id)}
                >
                  {strategy.status === 'active' ? '停止' : '执行'}
                </ActionButton>
              </ActionButtons>
            </StrategyCard>
          ))
        )}
      </PageSection>
    )
  }

  // 🎯 渲染监控室页面
  const renderMonitoringPage = () => (
    <PageSection>
      <SectionTitle>
        📡 监控室
      </SectionTitle>
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center', 
        background: '#f8fafc', 
        borderRadius: '12px',
        border: '1px solid #e2e8f0'
      }}>
        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📡</div>
        <div style={{ color: '#666' }}>实时监控功能开发中...</div>
      </div>
    </PageSection>
  )

  // 🎯 渲染实验室页面
  const renderLabPage = () => (
    <PageSection>
      <SectionTitle>
        🧪 实验室
      </SectionTitle>
      <div style={{ display: 'grid', gap: '1rem' }}>
        <ActionButton
          $variant="primary"
          style={{ width: '100%', padding: '1rem' }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => switchScene('StrategyCreation')}
        >
          🎯 创建新策略
        </ActionButton>
        <ActionButton
          $variant="secondary"
          style={{ width: '100%', padding: '1rem' }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => switchScene('StrategyOptimization')}
        >
          📈 策略优化
        </ActionButton>
        <ActionButton
          $variant="secondary"
          style={{ width: '100%', padding: '1rem' }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => switchScene('Backtest')}
        >
          📊 回测分析
        </ActionButton>
      </div>
    </PageSection>
  )

  console.log('Rendering StrategyConsoleScene, currentPage:', currentPage)

  return (
    <UnifiedMobileNav title="策略控制台">
      <Container>
        {/* 标签页导航 */}
        <TabBar>
          {pages.map((page) => (
            <TabButton
              key={page.key}
              $active={currentPage === page.key}
              onClick={() => {
                console.log('Switching to page:', page.key)
                setCurrentPage(page.key)
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span style={{ marginRight: '0.5rem' }}>{page.icon}</span>
              {page.label}
            </TabButton>
          ))}
        </TabBar>

        {/* 内容区域 */}
        <ContentArea>
          {currentPage === 'strategies' && renderStrategiesPage()}
          {currentPage === 'monitoring' && renderMonitoringPage()}
          {currentPage === 'lab' && renderLabPage()}
        </ContentArea>
      </Container>
    </UnifiedMobileNav>
  )
}

export default StrategyConsoleScene
